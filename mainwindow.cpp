#include "mainwindow.h"
#include <QPainter>
#include <QtMath>
#include <QDebug>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
{
    // 数据初始化（和你的Python数据类似）
    curves = {
        {QPointF(9794352.00, -0.67), QPointF(0.00, 0.00), -10454268.67},
        {QPointF(0.00, 0.00), QPointF(4778222.00, 8779192.67), -10454268.67},
        {QPointF(4778222.00, 8779192.67), QPointF(181229.33, 17438526.00), 0.00},
        {QPointF(181229.33, 17438526.00), QPointF(180452.00, 17449800.00), 0.00},
        {QPointF(180452.00, 17449800.00), QPointF(9773872.67, 17449799.33), 0.00},
        {QPointF(9773872.67, 17449799.33), QPointF(9794352.00, -0.67), 0.00}
    };
    resize(800, 1200);
}

MainWindow::~MainWindow() {}

void MainWindow::paintEvent(QPaintEvent *)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    painter.setPen(QPen(Qt::black, 2));

    // 改进的坐标缩放和平移
    double scale = 0.00005;
    double offsetX = 50;
    double offsetY = 1000;

    for (const auto& seg : curves) {
        QPointF p0 = QPointF(seg.p0.x() * scale + offsetX, offsetY - seg.p0.y() * scale);
        QPointF p1 = QPointF(seg.p1.x() * scale + offsetX, offsetY - seg.p1.y() * scale);

        if (qFuzzyIsNull(seg.radius)) {
            // 直线段
            painter.drawLine(p0, p1);
        } else {
            // 圆弧段 - 重构的逻辑
            drawArcSegment(painter, seg, scale, offsetX, offsetY);
        }
    }
}

void MainWindow::drawArcSegment(QPainter& painter, const CurveSegment& seg,
                               double scale, double offsetX, double offsetY)
{
    double x0 = seg.p0.x(), y0 = seg.p0.y();
    double x1 = seg.p1.x(), y1 = seg.p1.y();
    double r = seg.radius;

    // 计算弦长
    double chord_length = std::hypot(x1 - x0, y1 - y0);

    // 检查半径是否足够大
    if (std::abs(r) < chord_length / 2) {
        // 半径太小，退化为直线
        QPointF p0_screen(x0 * scale + offsetX, offsetY - y0 * scale);
        QPointF p1_screen(x1 * scale + offsetX, offsetY - y1 * scale);
        painter.drawLine(p0_screen, p1_screen);
        return;
    }

    // 计算圆心
    double mid_x = (x0 + x1) / 2;
    double mid_y = (y0 + y1) / 2;
    double abs_r = std::abs(r);
    double h = std::sqrt(abs_r * abs_r - (chord_length / 2) * (chord_length / 2));

    // 计算垂直于弦的单位向量
    double chord_dx = x1 - x0;
    double chord_dy = y1 - y0;
    double perp_dx = -chord_dy / chord_length;
    double perp_dy = chord_dx / chord_length;

    // 根据半径正负确定圆心位置
    double center_x, center_y;
    if (r < 0) {
        // 负半径：逆时针方向
        center_x = mid_x + h * perp_dx;
        center_y = mid_y + h * perp_dy;
    } else {
        // 正半径：顺时针方向
        center_x = mid_x - h * perp_dx;
        center_y = mid_y - h * perp_dy;
    }

    // 计算起止角度（弧度）
    double start_angle = std::atan2(y0 - center_y, x0 - center_x);
    double end_angle = std::atan2(y1 - center_y, x1 - center_x);

    // 计算角度差，正确处理负半径的情况
    double angle_span;
    if (r < 0) {
        // 负半径：逆时针，角度应该是负的
        angle_span = end_angle - start_angle;
        if (angle_span > 0) {
            angle_span -= 2 * M_PI;
        }
    } else {
        // 正半径：顺时针，角度应该是正的
        angle_span = end_angle - start_angle;
        if (angle_span < 0) {
            angle_span += 2 * M_PI;
        }
    }

    // 转换到屏幕坐标
    double cx = center_x * scale + offsetX;
    double cy = offsetY - center_y * scale;
    double radius_px = abs_r * scale;

    // Qt的角度系统：0度在3点钟方向，正角度为顺时针
    // 需要转换角度系统：数学角度系统 -> Qt角度系统
    double qt_start_angle = -start_angle * 180.0 / M_PI;
    double qt_span_angle = -angle_span * 180.0 / M_PI;

    // 创建包围矩形
    QRectF rect(cx - radius_px, cy - radius_px, 2 * radius_px, 2 * radius_px);

    // 绘制圆弧（Qt角度单位为1/16度）
    painter.drawArc(rect, int(qt_start_angle * 16), int(qt_span_angle * 16));
}
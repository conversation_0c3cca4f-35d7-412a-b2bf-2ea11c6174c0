#pragma once

#include <QGraphicsItem>
#include <QPointF>
#include <QPainter>
#include <QStyleOptionGraphicsItem>
#include <QWidget>
#include <QtMath>

class CurveGraphicsItem : public QGraphicsItem
{
public:
    CurveGraphicsItem(const QPointF& startPoint, const QPointF& endPoint, 
                      double radius, double scale = 1.0, QGraphicsItem *parent = nullptr);

    QRectF boundingRect() const override;
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;

    // 设置绘制参数
    void setPen(const QPen& pen);
    void setScale(double scale);

private:
    struct ArcParams {
        QPointF center;
        double radius;
        double startAngle;
        double spanAngle;
        bool isValid;
    };

    ArcParams calculateArcParams(const QPointF& p0, const QPointF& p1, double radius) const;
    
    QPointF m_startPoint;
    QPointF m_endPoint;
    double m_radius;
    double m_scale;
    QPen m_pen;
    mutable QRectF m_boundingRect;
    mutable bool m_boundingRectValid;
};

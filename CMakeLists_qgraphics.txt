cmake_minimum_required(VERSION 3.14)
project(QtGraphicsCurveDemo)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# 设置Qt路径（根据您的Qt安装路径调整）
set(CMAKE_PREFIX_PATH C:/Qt/5.15.2/msvc2019)

find_package(Qt5 COMPONENTS Widgets REQUIRED)

add_executable(QtGraphicsCurveDemo
    qgraphics_main.cpp
    qgraphics_mainwindow.cpp
    qgraphics_mainwindow.h
    curve_graphics_item.cpp
    curve_graphics_item.h
)

target_link_libraries(QtGraphicsCurveDemo Qt5::Widgets)

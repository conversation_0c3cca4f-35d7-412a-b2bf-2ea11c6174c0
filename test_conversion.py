#!/usr/bin/env python3
"""
测试Python到Qt转换的正确性
比较Python版本和Qt版本的圆弧计算结果
"""

import numpy as np
import math

def python_get_arc_params(x0, y0, x1, y1, radius):
    """Python版本的圆弧参数计算（来自script.py）"""
    # 计算弦长
    chord_length = np.sqrt((x1 - x0)**2 + (y1 - y0)**2)
    if abs(radius) < chord_length / 2:
        return None  # 半径太小，退化为直线

    # 计算弦中点
    mid_x = (x0 + x1) / 2
    mid_y = (y0 + y1) / 2

    # 计算从弦中点到圆心的距离
    abs_radius = abs(radius)
    h = np.sqrt(abs_radius**2 - (chord_length / 2)**2)

    # 计算垂直于弦的单位向量
    chord_dx = x1 - x0
    chord_dy = y1 - y0
    perp_dx = -chord_dy / chord_length
    perp_dy = chord_dx / chord_length

    # 根据半径正负确定圆心
    if radius < 0:
        # 负半径：逆时针方向
        center_x = mid_x + h * perp_dx
        center_y = mid_y + h * perp_dy
    else:
        # 正半径：顺时针方向
        center_x = mid_x - h * perp_dx
        center_y = mid_y - h * perp_dy

    # 计算起止角（以度为单位，matplotlib Arc 需要）
    start_angle = np.degrees(np.arctan2(y0 - center_y, x0 - center_x))
    end_angle = np.degrees(np.arctan2(y1 - center_y, x1 - center_x))

    # 正确处理角度差，考虑半径正负
    if radius < 0:
        # 负半径：逆时针，matplotlib的Arc需要theta2 > theta1
        # 但我们需要确保是逆时针方向
        angle_diff = end_angle - start_angle
        if angle_diff <= 0:
            angle_diff += 360
        theta1 = start_angle
        theta2 = start_angle + angle_diff
    else:
        # 正半径：顺时针，需要调整角度
        angle_diff = end_angle - start_angle
        if angle_diff <= 0:
            angle_diff += 360
        theta1 = start_angle + angle_diff
        theta2 = start_angle 

    return center_x, center_y, abs_radius, theta1, theta2

def qt_calculate_arc_params(x0, y0, x1, y1, radius):
    """Qt版本的圆弧参数计算（模拟curve_graphics_item.cpp中的逻辑）"""
    # 计算弦长
    chord_length = math.sqrt((x1 - x0) * (x1 - x0) + (y1 - y0) * (y1 - y0))
    if abs(radius) < chord_length / 2:
        return None  # 半径太小，无效
    
    # 计算弦中点
    mid_x = (x0 + x1) / 2
    mid_y = (y0 + y1) / 2
    
    # 计算从弦中点到圆心的距离
    abs_radius = abs(radius)
    h = math.sqrt(abs_radius * abs_radius - (chord_length / 2) * (chord_length / 2))
    
    # 计算垂直于弦的单位向量
    chord_dx = x1 - x0
    chord_dy = y1 - y0
    perp_dx = -chord_dy / chord_length
    perp_dy = chord_dx / chord_length
    
    # 根据半径正负确定圆心
    if radius < 0:
        # 负半径：逆时针方向
        center_x = mid_x + h * perp_dx
        center_y = mid_y + h * perp_dy
    else:
        # 正半径：顺时针方向
        center_x = mid_x - h * perp_dx
        center_y = mid_y - h * perp_dy
    
    # 计算起止角（以度为单位）
    start_angle = math.atan2(y0 - center_y, x0 - center_x) * 180.0 / math.pi
    end_angle = math.atan2(y1 - center_y, x1 - center_x) * 180.0 / math.pi
    
    # 计算角度跨度，考虑半径正负
    angle_diff = end_angle - start_angle
    if radius < 0:
        # 负半径：逆时针
        if angle_diff <= 0:
            angle_diff += 360
        start_angle_qt = start_angle
        span_angle_qt = angle_diff
    else:
        # 正半径：顺时针
        if angle_diff >= 0:
            angle_diff -= 360
        start_angle_qt = start_angle
        span_angle_qt = angle_diff
    
    return center_x, center_y, abs_radius, start_angle_qt, span_angle_qt

def test_arc_calculations():
    """测试圆弧计算的一致性"""
    print("测试Python和Qt版本的圆弧计算一致性")
    print("=" * 60)
    
    # 测试数据（来自curves）
    test_cases = [
        # (起点, 终点, 半径)
        ((9794352.00, -0.67), (0.00, 0.00), -10454268.67),
        ((0.00, 0.00), (4778222.00, 8779192.67), -10454268.67),
        ((21513800.0, 3362410.0), (17360900.0, 9131300.0), 6083300.0),
    ]
    
    for i, ((x0, y0), (x1, y1), radius) in enumerate(test_cases):
        print(f"\n测试案例 {i+1}:")
        print(f"起点: ({x0}, {y0})")
        print(f"终点: ({x1}, {y1})")
        print(f"半径: {radius}")
        
        # Python版本计算
        python_result = python_get_arc_params(x0, y0, x1, y1, radius)
        
        # Qt版本计算
        qt_result = qt_calculate_arc_params(x0, y0, x1, y1, radius)
        
        if python_result is None and qt_result is None:
            print("✓ 两个版本都认为半径太小，退化为直线")
            continue
        elif python_result is None or qt_result is None:
            print("✗ 一个版本返回None，另一个版本有结果")
            continue
        
        # 比较结果
        py_cx, py_cy, py_r, py_theta1, py_theta2 = python_result
        qt_cx, qt_cy, qt_r, qt_start, qt_span = qt_result
        
        print(f"Python结果:")
        print(f"  圆心: ({py_cx:.2f}, {py_cy:.2f})")
        print(f"  半径: {py_r:.2f}")
        print(f"  角度: {py_theta1:.2f}° 到 {py_theta2:.2f}°")
        
        print(f"Qt结果:")
        print(f"  圆心: ({qt_cx:.2f}, {qt_cy:.2f})")
        print(f"  半径: {qt_r:.2f}")
        print(f"  起始角: {qt_start:.2f}°, 跨度: {qt_span:.2f}°")
        
        # 检查一致性
        center_diff = math.sqrt((py_cx - qt_cx)**2 + (py_cy - qt_cy)**2)
        radius_diff = abs(py_r - qt_r)
        
        tolerance = 1e-6
        if center_diff < tolerance and radius_diff < tolerance:
            print("✓ 圆心和半径计算一致")
        else:
            print(f"✗ 差异过大 - 圆心差异: {center_diff:.2e}, 半径差异: {radius_diff:.2e}")

def main():
    print("Python到Qt QGraphics转换验证")
    print("=" * 60)
    
    print("\n这个脚本验证Python版本和Qt版本的数学计算是否一致。")
    print("主要检查圆弧参数计算的正确性。")
    
    test_arc_calculations()
    
    print("\n" + "=" * 60)
    print("验证完成！")
    print("\n如果所有测试都通过，说明Qt版本正确实现了Python版本的算法。")

if __name__ == "__main__":
    main()

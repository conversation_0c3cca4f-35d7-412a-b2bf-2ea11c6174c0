#include "qgraphics_mainwindow.h"
#include "curve_graphics_item.h"
#include <QMenuBar>
#include <QAction>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QDebug>
#include <QtMath>

QGraphicsMainWindow::QGraphicsMainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_view(nullptr)
    , m_scene(nullptr)
    , m_scale(0.00005)
    , m_offsetX(50)
    , m_offsetY(1000)
{
    setupUI();
    setupData();
    drawCurves();
}

QGraphicsMainWindow::~QGraphicsMainWindow()
{
}

void QGraphicsMainWindow::setupUI()
{
    // 创建中央窗口部件
    QWidget *centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);
    
    // 创建布局
    QVBoxLayout *mainLayout = new QVBoxLayout(centralWidget);
    
    // 创建控制按钮布局
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    
    QPushButton *resetBtn = new QPushButton("重置视图", this);
    QPushButton *zoomInBtn = new QPushButton("放大", this);
    QPushButton *zoomOutBtn = new QPushButton("缩小", this);
    
    buttonLayout->addWidget(resetBtn);
    buttonLayout->addWidget(zoomInBtn);
    buttonLayout->addWidget(zoomOutBtn);
    buttonLayout->addStretch();
    
    // 创建QGraphicsView和QGraphicsScene
    m_scene = new QGraphicsScene(this);
    m_view = new QGraphicsView(m_scene, this);
    
    // 设置视图属性
    m_view->setRenderHint(QPainter::Antialiasing);
    m_view->setDragMode(QGraphicsView::RubberBandDrag);
    m_view->setInteractive(true);
    
    // 添加到布局
    mainLayout->addLayout(buttonLayout);
    mainLayout->addWidget(m_view);
    
    // 连接信号槽
    connect(resetBtn, &QPushButton::clicked, this, &QGraphicsMainWindow::resetView);
    connect(zoomInBtn, &QPushButton::clicked, this, &QGraphicsMainWindow::zoomIn);
    connect(zoomOutBtn, &QPushButton::clicked, this, &QGraphicsMainWindow::zoomOut);
    
    // 设置窗口属性
    setWindowTitle("Qt QGraphics 曲线绘制");
    resize(1000, 800);
    
    addMenuBar();
}

void QGraphicsMainWindow::setupData()
{
    // 初始化curves数据（对应Python中的curves）
    m_curves = {
        {QPointF(9794352.00, -0.67), 0.00},
        {QPointF(0.00, 0.00), -10454268.67},
        {QPointF(4778222.00, 8779192.67), -10454268.67},
        {QPointF(181229.33, 17438526.00), 0.00},
        {QPointF(180452.00, 17449800.00), 0.00},
        {QPointF(9773872.67, 17449799.33), 0.00},
        {QPointF(9794352.00, -0.67), 0.00}
    };
    
    // 初始化curves2数据（对应Python中的curves2）
    m_curves2 = {
        {QPointF(26017738.67, 17348200.0), 0.0},
        {QPointF(25992338.67, 304800.0), 0.0},
        {QPointF(21513800.0, 304800.0), 0.0},
        {QPointF(21513800.0, 3362410.0), 6083300.0},
        {QPointF(17360900.0, 9131300.0), 6083300.0},
        {QPointF(21564600.0, 14916941.33), 0.0},
        {QPointF(21564600.0, 17348200.0), 0.0},
        {QPointF(26017738.67, 17348200.0), 0.0}
    };
}

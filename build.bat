@echo off
echo 正在构建几何图形渲染器...

REM 创建构建目录
if not exist build mkdir build
cd build

REM 运行CMake配置
echo 配置CMake...
cmake .. -G "MinGW Makefiles"
if %errorlevel% neq 0 (
    echo CMake配置失败！
    pause
    exit /b 1
)

REM 编译项目
echo 编译项目...
cmake --build . --config Release
if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 构建完成！
echo 可执行文件位于: build\bin\GeometryRenderer.exe

REM 运行程序
echo 是否立即运行程序？ (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    if exist bin\GeometryRenderer.exe (
        echo 启动程序...
        bin\GeometryRenderer.exe
    ) else (
        echo 找不到可执行文件！
    )
)

pause

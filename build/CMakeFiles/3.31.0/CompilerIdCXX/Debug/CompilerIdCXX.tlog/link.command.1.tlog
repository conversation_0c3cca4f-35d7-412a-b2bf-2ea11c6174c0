^C:\USERS\<USER>\PYCHARMMISCPROJECT\BUILD\CMAKEFILES\3.31.0\COMPILERIDCXX\DEBUG\CMAKECXXCOMPILERID.OBJ
/OUT:".\COMPILERIDCXX.EXE" /INCREMENTAL:NO /NOLOGO /LIBPATH:"C:\USERS\<USER>\SCOOP\APPS\VCPKG\CURRENT\INSTALLED\X64-WINDOWS\LIB" /LIBPATH:"C:\USERS\<USER>\SCOOP\APPS\VCPKG\CURRENT\INSTALLED\X64-WINDOWS\LIB\MANUAL-LINK" KERNEL32.LIB USER32.LIB GDI32.LIB WINSPOOL.LIB COMDLG32.LIB ADVAPI32.LIB SHELL32.LIB OLE32.LIB OLEAUT32.LIB UUID.LIB ODBC32.LIB ODBCCP32.LIB "C:\USERS\<USER>\SCOOP\APPS\VCPKG\CURRENT\INSTALLED\X64-WINDOWS\LIB\*.LIB" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\COMPILERIDCXX.PDB" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\COMPILERIDCXX.LIB" /MACHINE:X64 DEBUG\CMAKECXXCOMPILERID.OBJ

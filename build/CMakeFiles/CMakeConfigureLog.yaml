
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.19+0d9f5a35a
      鐢熸垚鍚姩鏃堕棿涓?2025/7/2 18:53:20銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\lz592\\PyCharmMiscProject\\build\\CMakeFiles\\3.31.0\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdCXX.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      VcpkgTripletSelection:
        Using triplet "x64-windows" from "C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\"
        Using normalized configuration "Release"
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\lib" /LIBPATH:"C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\3.31.0\\CompilerIdCXX\\CompilerIdCXX.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\3.31.0\\CompilerIdCXX\\CompilerIdCXX.exe" "C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\lz592\\PyCharmMiscProject\\build\\CMakeFiles\\3.31.0\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:03.06
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/3.31.0/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-o1j824"
      binary: "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-o1j824"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-o1j824'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_63766.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.19+0d9f5a35a
        鐢熸垚鍚姩鏃堕棿涓?2025/7/2 18:53:23銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\lz592\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o1j824\\cmTC_63766.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_63766.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\lz592\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o1j824\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_63766.dir\\Debug\\cmTC_63766.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_63766.dir\\Debug\\cmTC_63766.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_63766.dir\\Debug\\cmTC_63766.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_63766.dir\\Debug\\\\" /Fd"cmTC_63766.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\scoop\\apps\\cmake\\3.31.0\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.43.34809 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_63766.dir\\Debug\\\\" /Fd"cmTC_63766.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\scoop\\apps\\cmake\\3.31.0\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o1j824\\Debug\\cmTC_63766.exe" /INCREMENTAL /ILK:"cmTC_63766.dir\\Debug\\cmTC_63766.ilk" /NOLOGO /LIBPATH:"C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\debug\\lib" /LIBPATH:"C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\debug\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-o1j824/Debug/cmTC_63766.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-o1j824/Debug/cmTC_63766.lib" /MACHINE:X64  /machine:x64 cmTC_63766.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_63766.vcxproj -> C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o1j824\\Debug\\cmTC_63766.exe
        AppLocalFromInstalled:
          pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o1j824\\Debug\\cmTC_63766.exe" "C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\debug\\bin" "cmTC_63766.dir\\Debug\\cmTC_63766.tlog\\cmTC_63766.write.1u.tlog" "cmTC_63766.dir\\Debug\\vcpkg.applocal.log"
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_63766.dir\\Debug\\cmTC_63766.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_63766.dir\\Debug\\cmTC_63766.tlog\\cmTC_63766.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\lz592\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o1j824\\cmTC_63766.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:02.31
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.43.34809.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/FindThreads.cmake:99 (CHECK_CXX_SOURCE_COMPILES)"
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:34 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:136 (include)"
      - "CMakeLists.txt:13 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-lfy5s0"
      binary: "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-lfy5s0"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6;C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-lfy5s0'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_7c089.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.19+0d9f5a35a
        鐢熸垚鍚姩鏃堕棿涓?2025/7/2 18:54:18銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\lz592\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lfy5s0\\cmTC_7c089.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_7c089.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\lz592\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lfy5s0\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_7c089.dir\\Debug\\cmTC_7c089.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_7c089.dir\\Debug\\cmTC_7c089.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_7c089.dir\\Debug\\cmTC_7c089.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_7c089.dir\\Debug\\\\" /Fd"cmTC_7c089.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lfy5s0\\src.cxx"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.43.34809 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_7c089.dir\\Debug\\\\" /Fd"cmTC_7c089.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lfy5s0\\src.cxx"
          src.cxx
        C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lfy5s0\\src.cxx(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減thread.h鈥? No such file or directory [C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lfy5s0\\cmTC_7c089.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\lz592\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lfy5s0\\cmTC_7c089.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\lz592\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lfy5s0\\cmTC_7c089.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lfy5s0\\src.cxx(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減thread.h鈥? No such file or directory [C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lfy5s0\\cmTC_7c089.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.59
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:34 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:136 (include)"
      - "CMakeLists.txt:13 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-zr47ip"
      binary: "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-zr47ip"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6;C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-zr47ip'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_b1b84.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.19+0d9f5a35a
        鐢熸垚鍚姩鏃堕棿涓?2025/7/2 18:54:19銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\lz592\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zr47ip\\cmTC_b1b84.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_b1b84.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\lz592\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zr47ip\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_b1b84.dir\\Debug\\cmTC_b1b84.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_b1b84.dir\\Debug\\cmTC_b1b84.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_b1b84.dir\\Debug\\cmTC_b1b84.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_b1b84.dir\\Debug\\\\" /Fd"cmTC_b1b84.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zr47ip\\CheckFunctionExists.cxx"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.43.34809 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_b1b84.dir\\Debug\\\\" /Fd"cmTC_b1b84.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zr47ip\\CheckFunctionExists.cxx"
          CheckFunctionExists.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zr47ip\\Debug\\cmTC_b1b84.exe" /INCREMENTAL /ILK:"cmTC_b1b84.dir\\Debug\\cmTC_b1b84.ilk" /NOLOGO /LIBPATH:"C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\debug\\lib" /LIBPATH:"C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\debug\\lib\\manual-link" pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-zr47ip/Debug/cmTC_b1b84.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-zr47ip/Debug/cmTC_b1b84.lib" /MACHINE:X64  /machine:x64 cmTC_b1b84.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減threads.lib鈥?[C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zr47ip\\cmTC_b1b84.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\lz592\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zr47ip\\cmTC_b1b84.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\lz592\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zr47ip\\cmTC_b1b84.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減threads.lib鈥?[C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zr47ip\\cmTC_b1b84.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.72
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:34 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:136 (include)"
      - "CMakeLists.txt:13 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-ov0vwd"
      binary: "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-ov0vwd"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6;C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-ov0vwd'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_bcc8c.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.19+0d9f5a35a
        鐢熸垚鍚姩鏃堕棿涓?2025/7/2 18:54:20銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\lz592\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ov0vwd\\cmTC_bcc8c.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_bcc8c.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\lz592\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ov0vwd\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_bcc8c.dir\\Debug\\cmTC_bcc8c.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_bcc8c.dir\\Debug\\cmTC_bcc8c.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_bcc8c.dir\\Debug\\cmTC_bcc8c.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_bcc8c.dir\\Debug\\\\" /Fd"cmTC_bcc8c.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ov0vwd\\CheckFunctionExists.cxx"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.43.34809 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_bcc8c.dir\\Debug\\\\" /Fd"cmTC_bcc8c.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ov0vwd\\CheckFunctionExists.cxx"
          CheckFunctionExists.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ov0vwd\\Debug\\cmTC_bcc8c.exe" /INCREMENTAL /ILK:"cmTC_bcc8c.dir\\Debug\\cmTC_bcc8c.ilk" /NOLOGO /LIBPATH:"C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\debug\\lib" /LIBPATH:"C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\debug\\lib\\manual-link" pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-ov0vwd/Debug/cmTC_bcc8c.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-ov0vwd/Debug/cmTC_bcc8c.lib" /MACHINE:X64  /machine:x64 cmTC_bcc8c.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減thread.lib鈥?[C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ov0vwd\\cmTC_bcc8c.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\lz592\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ov0vwd\\cmTC_bcc8c.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\lz592\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ov0vwd\\cmTC_bcc8c.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減thread.lib鈥?[C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ov0vwd\\cmTC_bcc8c.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.70
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/FindWrapAtomic.cmake:36 (check_cxx_source_compiles)"
      - "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake:45 (include)"
      - "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:181 (find_package)"
      - "CMakeLists.txt:13 (find_package)"
    checks:
      - "Performing Test HAVE_STDATOMIC"
    directories:
      source: "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-w3945p"
      binary: "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-w3945p"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6;C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "HAVE_STDATOMIC"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-w3945p'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_ba495.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.19+0d9f5a35a
        鐢熸垚鍚姩鏃堕棿涓?2025/7/2 18:54:21銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\lz592\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w3945p\\cmTC_ba495.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_ba495.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\lz592\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w3945p\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_ba495.dir\\Debug\\cmTC_ba495.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_ba495.dir\\Debug\\cmTC_ba495.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_ba495.dir\\Debug\\cmTC_ba495.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_STDATOMIC /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_ba495.dir\\Debug\\\\" /Fd"cmTC_ba495.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w3945p\\src.cxx"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.43.34809 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_STDATOMIC /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_ba495.dir\\Debug\\\\" /Fd"cmTC_ba495.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w3945p\\src.cxx"
          src.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w3945p\\Debug\\cmTC_ba495.exe" /INCREMENTAL /ILK:"cmTC_ba495.dir\\Debug\\cmTC_ba495.ilk" /NOLOGO /LIBPATH:"C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\debug\\lib" /LIBPATH:"C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\debug\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-w3945p/Debug/cmTC_ba495.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-w3945p/Debug/cmTC_ba495.lib" /MACHINE:X64  /machine:x64 cmTC_ba495.dir\\Debug\\src.obj
          cmTC_ba495.vcxproj -> C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w3945p\\Debug\\cmTC_ba495.exe
        AppLocalFromInstalled:
          pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w3945p\\Debug\\cmTC_ba495.exe" "C:\\Users\\<USER>\\scoop\\apps\\vcpkg\\current\\installed\\x64-windows\\debug\\bin" "cmTC_ba495.dir\\Debug\\cmTC_ba495.tlog\\cmTC_ba495.write.1u.tlog" "cmTC_ba495.dir\\Debug\\vcpkg.applocal.log"
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_ba495.dir\\Debug\\cmTC_ba495.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_ba495.dir\\Debug\\cmTC_ba495.tlog\\cmTC_ba495.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\lz592\\PyCharmMiscProject\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w3945p\\cmTC_ba495.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:03.04
        
      exitCode: 0
...

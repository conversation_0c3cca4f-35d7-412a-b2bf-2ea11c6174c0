cmake_minimum_required(VERSION 3.31.0.0)
set(CMAKE_MODULE_PATH "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6;C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin")
cmake_policy(SET CMP0091 NEW)
cmake_policy(SET CMP0141 OLD)
cmake_policy(SET CMP0128 OLD)
project(CMAKE_TRY_COMPILE CXX)
set(CMAKE_VERBOSE_MAKEFILE 1)
set(CMAKE_CXX_FLAGS "/DWIN32 /D_WINDOWS /GR /EHsc")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${COMPILE_DEFINITIONS}")
set(CMAKE_CXX_FLAGS_DEBUG "/Zi /Ob0 /Od /RTC1")
set(CMAKE_EXE_LINKER_FLAGS "/machine:x64")
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} ${EXE_LINKER_FLAGS}")
include_directories(${INCLUDE_DIRECTORIES})
set(CMAKE_SUPPRESS_REGENERATION 1)
link_directories(${LINK_DIRECTORIES})
add_definitions([==[-DHAVE_STDATOMIC]==])
cmake_policy(SET CMP0065 NEW)
cmake_policy(SET CMP0083 NEW)
cmake_policy(SET CMP0155 OLD)
cmake_policy(SET CMP0157 OLD)
include("${CMAKE_ROOT}/Modules/Internal/HeaderpadWorkaround.cmake")
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-w3945p")
add_executable(cmTC_ba495)
target_sources(cmTC_ba495 PRIVATE
  "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/CMakeScratch/TryCompile-w3945p/src.cxx"
)
file(GENERATE OUTPUT "${CMAKE_BINARY_DIR}/cmTC_ba495_$<UPPER_CASE:$<CONFIG>>_loc"
     CONTENT $<TARGET_FILE:cmTC_ba495>)
set_property(TARGET cmTC_ba495 PROPERTY "CXX_STANDARD" "17")
set_property(TARGET cmTC_ba495 PROPERTY "CXX_STANDARD_REQUIRED" "ON")
target_link_libraries(cmTC_ba495 ${LINK_LIBRARIES})

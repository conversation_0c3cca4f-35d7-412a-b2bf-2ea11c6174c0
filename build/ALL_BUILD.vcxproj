<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{53FE51D6-6502-3D58-A538-0546FF5B11CB}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\PyCharmMiscProject\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/PyCharmMiscProject/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
C:\Users\<USER>\scoop\apps\cmake\3.31.0\bin\cmake.exe -SC:/Users/<USER>/PyCharmMiscProject -BC:/Users/<USER>/PyCharmMiscProject/build --check-stamp-file C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\FindWrapAtomic.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtInstallPaths.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessAliasTargets.cmake;C:\Users\<USER>\PyCharmMiscProject\build\CMakeFiles\3.31.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\PyCharmMiscProject\build\CMakeFiles\3.31.0\CMakeRCCompiler.cmake;C:\Users\<USER>\PyCharmMiscProject\build\CMakeFiles\3.31.0\CMakeSystem.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\FindThreads.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\FindVulkan.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\PyCharmMiscProject\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/PyCharmMiscProject/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
C:\Users\<USER>\scoop\apps\cmake\3.31.0\bin\cmake.exe -SC:/Users/<USER>/PyCharmMiscProject -BC:/Users/<USER>/PyCharmMiscProject/build --check-stamp-file C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\FindWrapAtomic.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtInstallPaths.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessAliasTargets.cmake;C:\Users\<USER>\PyCharmMiscProject\build\CMakeFiles\3.31.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\PyCharmMiscProject\build\CMakeFiles\3.31.0\CMakeRCCompiler.cmake;C:\Users\<USER>\PyCharmMiscProject\build\CMakeFiles\3.31.0\CMakeSystem.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\FindThreads.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\FindVulkan.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\PyCharmMiscProject\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/PyCharmMiscProject/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
C:\Users\<USER>\scoop\apps\cmake\3.31.0\bin\cmake.exe -SC:/Users/<USER>/PyCharmMiscProject -BC:/Users/<USER>/PyCharmMiscProject/build --check-stamp-file C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\FindWrapAtomic.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtInstallPaths.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessAliasTargets.cmake;C:\Users\<USER>\PyCharmMiscProject\build\CMakeFiles\3.31.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\PyCharmMiscProject\build\CMakeFiles\3.31.0\CMakeRCCompiler.cmake;C:\Users\<USER>\PyCharmMiscProject\build\CMakeFiles\3.31.0\CMakeSystem.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\FindThreads.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\FindVulkan.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\PyCharmMiscProject\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/PyCharmMiscProject/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
C:\Users\<USER>\scoop\apps\cmake\3.31.0\bin\cmake.exe -SC:/Users/<USER>/PyCharmMiscProject -BC:/Users/<USER>/PyCharmMiscProject/build --check-stamp-file C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\FindWrapAtomic.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtInstallPaths.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;C:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessAliasTargets.cmake;C:\Users\<USER>\PyCharmMiscProject\build\CMakeFiles\3.31.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\PyCharmMiscProject\build\CMakeFiles\3.31.0\CMakeRCCompiler.cmake;C:\Users\<USER>\PyCharmMiscProject\build\CMakeFiles\3.31.0\CMakeSystem.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\FindThreads.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\FindVulkan.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Users\<USER>\scoop\apps\cmake\3.31.0\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\PyCharmMiscProject\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\PyCharmMiscProject\build\ZERO_CHECK.vcxproj">
      <Project>{207F5B6F-AFB7-3E71-84A4-A888ABAD669D}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\PyCharmMiscProject\build\GeometryRenderer.vcxproj">
      <Project>{69124A50-A870-3D78-BFE3-402F6332615A}</Project>
      <Name>GeometryRenderer</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
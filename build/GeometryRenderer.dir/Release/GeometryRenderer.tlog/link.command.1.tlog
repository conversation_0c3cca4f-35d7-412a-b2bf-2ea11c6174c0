^C:\USERS\<USER>\PY<PERSON><PERSON>MMISCPROJECT\BUILD\GEOMETRYRENDERER.DIR\RELEASE\GEOMETRYRENDERER.OBJ|C:\USERS\<USER>\PYCHARMMISCPROJECT\BUILD\GEOMETRYRENDERER.DIR\RELEASE\MAIN.OBJ|C:\USERS\<USER>\PYCHARMMISCPROJECT\BUILD\GEOMETRYRENDERER.DIR\RELEASE\MAINWINDOW.OBJ|C:\USERS\<USER>\PYCHARMMISCPROJECT\BUILD\GEOMETRYRENDERER.DIR\RELEASE\MOCS_COMPILATION_RELEASE.OBJ
/OUT:"C:\USERS\<USER>\PYCHARMMISCPROJECT\BUILD\BIN\RELEASE\GEOMETRYRENDERER.EXE" /INCREMENTAL:NO /NOLOGO /LIBPATH:"C:\USERS\<USER>\SCOOP\APPS\VCPKG\CURRENT\INSTALLED\X64-WINDOWS\LIB" /LIBPATH:"C:\USERS\<USER>\SCOOP\APPS\VCPKG\CURRENT\INSTALLED\X64-WINDOWS\LIB\MANUAL-LINK" C:\QT\6.8.0\MSVC2022_64\LIB\QT6WIDGETS.LIB C:\QT\6.8.0\MSVC2022_64\LIB\QT6GUI.LIB C:\QT\6.8.0\MSVC2022_64\LIB\QT6CORE.LIB MPR.LIB USERENV.LIB C:\QT\6.8.0\MSVC2022_64\LIB\QT6ENTRYPOINT.LIB SHELL32.LIB D3D11.LIB DXGI.LIB DXGUID.LIB D3D12.LIB KERNEL32.LIB USER32.LIB GDI32.LIB WINSPOOL.LIB SHELL32.LIB OLE32.LIB OLEAUT32.LIB UUID.LIB COMDLG32.LIB ADVAPI32.LIB "C:\USERS\<USER>\SCOOP\APPS\VCPKG\CURRENT\INSTALLED\X64-WINDOWS\LIB\*.LIB" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:"C:/USERS/<USER>/PYCHARMMISCPROJECT/BUILD/BIN/RELEASE/GEOMETRYRENDERER.PDB" /SUBSYSTEM:WINDOWS /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/USERS/<USER>/PYCHARMMISCPROJECT/BUILD/RELEASE/GEOMETRYRENDERER.LIB" /MACHINE:X64  /machine:x64 GEOMETRYRENDERER.DIR\RELEASE\MAIN.OBJ
GEOMETRYRENDERER.DIR\RELEASE\MAINWINDOW.OBJ
GEOMETRYRENDERER.DIR\RELEASE\GEOMETRYRENDERER.OBJ
GEOMETRYRENDERER.DIR\RELEASE\MOCS_COMPILATION_RELEASE.OBJ

#include "GeometryRenderer.h"
#include <QWheelEvent>
#include <QMouseEvent>
#include <QPaintEvent>
#include <QPainterPath>
#include <QDebug>
#include <algorithm>
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

GeometryRenderer::GeometryRenderer(QWidget *parent)
    : QWidget(parent)
    , m_lineWidth(2)
    , m_lineColor(Qt::black)
    , m_showGrid(true)
    , m_scale(1.0)
    , m_offset(0, 0)
    , m_isPanning(false)
{
    setMinimumSize(800, 600);
    setMouseTracking(true);
}

void GeometryRenderer::setCurveData(const std::vector<CurveSegment>& curves) {
    m_curves = curves;
    m_worldBounds = calculateBoundingRect();
    fitToView();
    update();
}

void GeometryRenderer::paintEvent(QPaintEvent *event) {
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // 设置背景
    painter.fillRect(rect(), Qt::white);
    
    // 设置视口变换
    setupViewport(painter);
    
    // 渲染网格
    if (m_showGrid) {
        renderGrid(painter);
    }
    
    // 渲染曲线
    renderCurves(painter);
}

void GeometryRenderer::setupViewport(QPainter& painter) {
    painter.save();
    painter.translate(width() / 2.0 + m_offset.x(), height() / 2.0 + m_offset.y());
    painter.scale(m_scale, -m_scale); // Y轴翻转以匹配数学坐标系
}

void GeometryRenderer::renderGrid(QPainter& painter) {
    painter.save();
    
    QPen gridPen(Qt::lightGray);
    gridPen.setWidth(0); // 使用cosmetic pen，不受缩放影响
    painter.setPen(gridPen);
    
    // 计算网格间距
    double gridSpacing = 1000000.0; // 1M单位
    while (gridSpacing * m_scale < 20) {
        gridSpacing *= 10;
    }
    while (gridSpacing * m_scale > 200) {
        gridSpacing /= 10;
    }
    
    // 计算可见区域
    Point topLeft = screenToWorld(QPointF(0, 0));
    Point bottomRight = screenToWorld(QPointF(width(), height()));
    QRectF visibleRect(topLeft.x, topLeft.y,
                       bottomRight.x - topLeft.x, bottomRight.y - topLeft.y);
    
    // 绘制垂直网格线
    double startX = std::floor(visibleRect.left() / gridSpacing) * gridSpacing;
    for (double x = startX; x <= visibleRect.right(); x += gridSpacing) {
        painter.drawLine(QPointF(x, visibleRect.top()), QPointF(x, visibleRect.bottom()));
    }
    
    // 绘制水平网格线
    double startY = std::floor(visibleRect.top() / gridSpacing) * gridSpacing;
    for (double y = startY; y <= visibleRect.bottom(); y += gridSpacing) {
        painter.drawLine(QPointF(visibleRect.left(), y), QPointF(visibleRect.right(), y));
    }
    
    painter.restore();
}

void GeometryRenderer::renderCurves(QPainter& painter) {
    if (m_curves.empty()) return;
    
    painter.save();
    
    QPen pen(m_lineColor);
    pen.setWidth(0); // 使用cosmetic pen
    pen.setCapStyle(Qt::RoundCap);
    pen.setJoinStyle(Qt::RoundJoin);
    painter.setPen(pen);
    
    for (size_t i = 0; i < m_curves.size() - 1; ++i) {
        const auto& current = m_curves[i];
        const auto& next = m_curves[i + 1];
        
        Point start = current.point;
        Point end = next.point;
        double radius = current.radius;

        qDebug() << QString("段%1: 起点=(%2, %3), 终点=(%4, %5), 半径=%6")
                    .arg(i).arg(start.x).arg(start.y).arg(end.x).arg(end.y).arg(radius);
        
        if (std::abs(radius) < 1e-6) {
            // 直线段
            painter.drawLine(worldToScreen(start), worldToScreen(end));
        } else {

            // x = {double} -5676046.6699999971
            // y = {double} 8779192.8800161071
            // radius = {double} 10454268.67
            // startAngle = {double} -57.115958045637846
            // endAngle = {double} -0.0000011510165833783503

            // 圆弧段
            ArcParams arcParams = calculateArcParams(start, end, radius);
            if (arcParams.isValid) {
                qDebug() << QString("  圆心=(%1, %2), 半径=%3, 起始角=%4, 结束角=%5")
                            .arg(arcParams.center.x).arg(arcParams.center.y)
                            .arg(arcParams.radius).arg(arcParams.startAngle).arg(arcParams.endAngle);

                // 转换为屏幕坐标
                QPointF centerScreen = worldToScreen(arcParams.center);
                double radiusScreen = arcParams.radius * m_scale;

                // 创建边界矩形
                QRectF boundingRect(
                    centerScreen.x() - radiusScreen,
                    centerScreen.y() - radiusScreen,
                    2 * radiusScreen,
                    2 * radiusScreen
                );

                // 使用与Python版本相同的角度计算逻辑
                double theta1 = arcParams.startAngle;
                double theta2 = arcParams.endAngle;



                // 计算角度跨度（Qt使用1/16度为单位）
                double spanAngle = theta2 - theta1;

                // 确保角度跨度在合理范围内
                while (spanAngle > 360) spanAngle -= 360;
                while (spanAngle < -360) spanAngle += 360;

                // 使用QPainterPath确保弧线精确连接到起点和终点
                QPainterPath arcPath;
                QPointF startScreen = worldToScreen(start);
                QPointF endScreen = worldToScreen(end);

                // 从起点开始
                arcPath.moveTo(startScreen);

                // 计算在屏幕坐标系下的角度
                double startAngleScreen = std::atan2(startScreen.y() - centerScreen.y(),
                                                   startScreen.x() - centerScreen.x()) * 180.0 / M_PI;
                double endAngleScreen = std::atan2(endScreen.y() - centerScreen.y(),
                                                 endScreen.x() - centerScreen.x()) * 180.0 / M_PI;

                // 计算角度跨度，考虑半径正负
                double screenSpanAngle = endAngleScreen - startAngleScreen;
                if (radius < 0) {
                    // 负半径：逆时针
                    if (screenSpanAngle <= 0) screenSpanAngle += 360;
                } else {
                    // 正半径：顺时针
                    if (screenSpanAngle >= 0) screenSpanAngle -= 360;
                }

                // 使用arcTo确保精确连接
                arcPath.arcTo(boundingRect, startAngleScreen, screenSpanAngle);

                // 绘制路径
                painter.drawPath(arcPath);
            } else {
                // 半径太小，退化为直线
                painter.drawLine(worldToScreen(start), worldToScreen(end));
            }
        }
    }
    
    // 闭合图形（如果需要）
    if (m_curves.size() > 2) {
        const auto& last = m_curves.back();
        const auto& first = m_curves.front();
        if (std::abs(last.point.x - first.point.x) > 1e-6 || 
            std::abs(last.point.y - first.point.y) > 1e-6) {
            painter.drawLine(worldToScreen(last.point), worldToScreen(first.point));
        }
    }
    
    painter.restore();
}

ArcParams GeometryRenderer::calculateArcParams(const Point& start, const Point& end, double radius) const {
    // 计算弦长
    double dx = end.x - start.x;
    double dy = end.y - start.y;
    double chordLength = std::sqrt(dx * dx + dy * dy);

    // if (std::abs(radius) < chordLength / 2.0) {
    //     return ArcParams(); // 半径太小，返回无效参数
    // }

    // 计算弦中点
    double midX = (start.x + end.x) / 2.0;
    double midY = (start.y + end.y) / 2.0;

    // 计算从弦中点到圆心的距离
    double absRadius = std::abs(radius);
    double h = std::sqrt(absRadius * absRadius - (chordLength / 2.0) * (chordLength / 2.0));

    // 计算垂直于弦的单位向量
    double perpDx = -dy / chordLength;
    double perpDy = dx / chordLength;

    // 根据半径正负确定圆心
    Point center;
    if (radius < 0) {
        // 负半径：逆时针方向
        center.x = midX + h * perpDx;
        center.y = midY + h * perpDy;
    } else {
        // 正半径：顺时针方向
        center.x = midX - h * perpDx;
        center.y = midY - h * perpDy;
    }

    // 计算起止角（以度为单位）
    // 注意：由于Qt坐标系Y轴向下，需要调整角度计算
    double startAngle = std::atan2(start.y - center.y, start.x - center.x) * 180.0 / M_PI;
    double endAngle = std::atan2(end.y - center.y, end.x - center.x) * 180.0 / M_PI;

    double theta1, theta2;
    if (radius < 0) {
        // 负半径：逆时针，matplotlib的Arc需要theta2 > theta1
        double angleDiff = endAngle - startAngle;
        if (angleDiff <= 0) {
            angleDiff += 360;
        }
        theta1 = startAngle;
        theta2 = startAngle + angleDiff;
    } else {
        // 正半径：顺时针，需要调整角度
        double angleDiff = endAngle - startAngle;
        if (angleDiff <= 0) {
            angleDiff += 360;
        }
        theta1 = startAngle + angleDiff;
        theta2 = startAngle;
    }

    return ArcParams(center, absRadius, theta1, theta2);
}

QPointF GeometryRenderer::worldToScreen(const Point& worldPoint) const {
    // 应用视图变换：缩放 + 平移 + 坐标系翻转
    double screenX = worldPoint.x * m_scale + width() / 2.0 + m_offset.x();
    double screenY = -worldPoint.y * m_scale + height() / 2.0 + m_offset.y();
    return QPointF(screenX, screenY);
}

Point GeometryRenderer::screenToWorld(const QPointF& screenPoint) const {
    // 考虑视图变换的逆变换
    double worldX = (screenPoint.x() - width() / 2.0 - m_offset.x()) / m_scale;
    double worldY = -(screenPoint.y() - height() / 2.0 - m_offset.y()) / m_scale;
    return Point(worldX, worldY);
}

QRectF GeometryRenderer::calculateBoundingRect() const {
    if (m_curves.empty()) {
        return QRectF(-1000, -1000, 2000, 2000);
    }

    double minX = m_curves[0].point.x;
    double maxX = m_curves[0].point.x;
    double minY = m_curves[0].point.y;
    double maxY = m_curves[0].point.y;

    for (const auto& curve : m_curves) {
        minX = std::min(minX, curve.point.x);
        maxX = std::max(maxX, curve.point.x);
        minY = std::min(minY, curve.point.y);
        maxY = std::max(maxY, curve.point.y);
    }

    // 添加一些边距
    double margin = std::max(maxX - minX, maxY - minY) * 0.1;
    return QRectF(minX - margin, minY - margin,
                  maxX - minX + 2 * margin, maxY - minY + 2 * margin);
}

void GeometryRenderer::fitToView() {
    if (m_worldBounds.isEmpty()) return;

    double scaleX = (width() - 40) / m_worldBounds.width();
    double scaleY = (height() - 40) / m_worldBounds.height();
    m_scale = std::min(scaleX, scaleY);

    // 居中显示
    m_offset = QPointF(
        -m_worldBounds.center().x() * m_scale,
        m_worldBounds.center().y() * m_scale
    );
}

void GeometryRenderer::wheelEvent(QWheelEvent *event) {
    const double scaleFactor = 1.15;

    if (event->angleDelta().y() > 0) {
        m_scale *= scaleFactor;
    } else {
        m_scale /= scaleFactor;
    }

    // 限制缩放范围
    m_scale = std::max(0.001, std::min(1000.0, m_scale));

    update();
    event->accept();
}

void GeometryRenderer::mousePressEvent(QMouseEvent *event) {
    if (event->button() == Qt::LeftButton) {
        m_isPanning = true;
        m_lastPanPoint = event->position();
        setCursor(Qt::ClosedHandCursor);
    }
}

void GeometryRenderer::mouseMoveEvent(QMouseEvent *event) {
    if (m_isPanning && (event->buttons() & Qt::LeftButton)) {
        QPointF delta = event->position() - m_lastPanPoint;
        m_offset += delta;
        m_lastPanPoint = event->position();
        update();
    }
}

void GeometryRenderer::mouseReleaseEvent(QMouseEvent *event) {
    if (event->button() == Qt::LeftButton) {
        m_isPanning = false;
        setCursor(Qt::ArrowCursor);
    }
}

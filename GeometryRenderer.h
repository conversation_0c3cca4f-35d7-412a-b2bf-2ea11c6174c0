#ifndef GEOMETRYRENDERER_H
#define GEOMETRYRENDERER_H

#include <QWidget>
#include <QPainter>
#include <QRectF>
#include <cmath>
#include "CurveData.h"

class GeometryRenderer : public QWidget {
    Q_OBJECT

public:
    explicit GeometryRenderer(QWidget *parent = nullptr);
    
    // 设置要渲染的曲线数据
    void setCurveData(const std::vector<CurveSegment>& curves);
    
    // 设置渲染参数
    void setLineWidth(int width) { m_lineWidth = width; update(); }
    void setLineColor(const QColor& color) { m_lineColor = color; update(); }
    void setShowGrid(bool show) { m_showGrid = show; update(); }

protected:
    void paintEvent(QPaintEvent *event) override;
    void wheelEvent(QWheelEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;

private:
    // 几何计算方法
    ArcParams calculateArcParams(const Point& start, const Point& end, double radius) const;
    
    // 渲染方法
    void renderCurves(QPainter& painter);
    void renderGrid(QPainter& painter);
    void setupViewport(QPainter& painter);
    
    // 坐标转换
    QPointF worldToScreen(const Point& worldPoint) const;
    Point screenToWorld(const QPointF& screenPoint) const;
    
    // 计算边界框
    QRectF calculateBoundingRect() const;
    void fitToView();

private:
    std::vector<CurveSegment> m_curves;
    
    // 渲染参数
    int m_lineWidth;
    QColor m_lineColor;
    bool m_showGrid;
    
    // 视图变换参数
    double m_scale;
    QPointF m_offset;
    QPointF m_lastPanPoint;
    bool m_isPanning;
    
    // 世界坐标边界
    QRectF m_worldBounds;
};

#endif // GEOMETRYRENDERER_H

#include "curve_graphics_item.h"
#include <QDebug>
#include <cmath>

CurveGraphicsItem::CurveGraphicsItem(const QPointF& startPoint, const QPointF& endPoint, 
                                     double radius, double scale, QGraphicsItem *parent)
    : QGraphicsItem(parent)
    , m_startPoint(startPoint)
    , m_endPoint(endPoint)
    , m_radius(radius)
    , m_scale(scale)
    , m_pen(QPen(Qt::black, 2))
    , m_boundingRectValid(false)
{
}

QRectF CurveGraphicsItem::boundingRect() const
{
    if (!m_boundingRectValid) {
        if (qFuzzyIsNull(m_radius)) {
            // 直线段的边界矩形
            double minX = qMin(m_startPoint.x(), m_endPoint.x());
            double maxX = qMax(m_startPoint.x(), m_endPoint.x());
            double minY = qMin(m_startPoint.y(), m_endPoint.y());
            double maxY = qMax(m_startPoint.y(), m_endPoint.y());
            
            double margin = m_pen.widthF() / 2.0;
            m_boundingRect = QRectF(minX - margin, minY - margin, 
                                   maxX - minX + 2 * margin, maxY - minY + 2 * margin);
        } else {
            // 圆弧段的边界矩形
            ArcParams params = calculateArcParams(m_startPoint, m_endPoint, m_radius);
            if (params.isValid) {
                double margin = m_pen.widthF() / 2.0;
                m_boundingRect = QRectF(params.center.x() - params.radius - margin,
                                       params.center.y() - params.radius - margin,
                                       2 * params.radius + 2 * margin,
                                       2 * params.radius + 2 * margin);
            } else {
                // 退化为直线
                double minX = qMin(m_startPoint.x(), m_endPoint.x());
                double maxX = qMax(m_startPoint.x(), m_endPoint.x());
                double minY = qMin(m_startPoint.y(), m_endPoint.y());
                double maxY = qMax(m_startPoint.y(), m_endPoint.y());
                
                double margin = m_pen.widthF() / 2.0;
                m_boundingRect = QRectF(minX - margin, minY - margin, 
                                       maxX - minX + 2 * margin, maxY - minY + 2 * margin);
            }
        }
        m_boundingRectValid = true;
    }
    return m_boundingRect;
}

void CurveGraphicsItem::paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget)
{
    Q_UNUSED(option)
    Q_UNUSED(widget)
    
    painter->setPen(m_pen);
    painter->setRenderHint(QPainter::Antialiasing);
    
    if (qFuzzyIsNull(m_radius)) {
        // 绘制直线段
        painter->drawLine(m_startPoint, m_endPoint);
    } else {
        // 绘制圆弧段
        ArcParams params = calculateArcParams(m_startPoint, m_endPoint, m_radius);
        if (params.isValid) {
            QRectF rect(params.center.x() - params.radius, 
                       params.center.y() - params.radius,
                       2 * params.radius, 2 * params.radius);
            
            // Qt的drawArc使用1/16度为单位
            int startAngle16 = static_cast<int>(params.startAngle * 16);
            int spanAngle16 = static_cast<int>(params.spanAngle * 16);
            
            painter->drawArc(rect, startAngle16, spanAngle16);
        } else {
            // 半径太小，退化为直线
            painter->drawLine(m_startPoint, m_endPoint);
        }
    }
}

void CurveGraphicsItem::setPen(const QPen& pen)
{
    m_pen = pen;
    m_boundingRectValid = false;
    update();
}

void CurveGraphicsItem::setScale(double scale)
{
    m_scale = scale;
    m_boundingRectValid = false;
    update();
}

CurveGraphicsItem::ArcParams CurveGraphicsItem::calculateArcParams(const QPointF& p0, const QPointF& p1, double radius) const
{
    ArcParams params;
    params.isValid = false;
    
    double x0 = p0.x(), y0 = p0.y();
    double x1 = p1.x(), y1 = p1.y();
    
    // 计算弦长
    double chord_length = std::sqrt((x1 - x0) * (x1 - x0) + (y1 - y0) * (y1 - y0));
    if (std::abs(radius) < chord_length / 2) {
        return params;  // 半径太小，无效
    }
    
    // 计算弦中点
    double mid_x = (x0 + x1) / 2;
    double mid_y = (y0 + y1) / 2;
    
    // 计算从弦中点到圆心的距离
    double abs_radius = std::abs(radius);
    double h = std::sqrt(abs_radius * abs_radius - (chord_length / 2) * (chord_length / 2));
    
    // 计算垂直于弦的单位向量
    double chord_dx = x1 - x0;
    double chord_dy = y1 - y0;
    double perp_dx = -chord_dy / chord_length;
    double perp_dy = chord_dx / chord_length;
    
    // 根据半径正负确定圆心
    double center_x, center_y;
    if (radius < 0) {
        // 负半径：逆时针方向
        center_x = mid_x + h * perp_dx;
        center_y = mid_y + h * perp_dy;
    } else {
        // 正半径：顺时针方向
        center_x = mid_x - h * perp_dx;
        center_y = mid_y - h * perp_dy;
    }
    
    // 计算起止角（以度为单位）
    double start_angle = std::atan2(y0 - center_y, x0 - center_x) * 180.0 / M_PI;
    double end_angle = std::atan2(y1 - center_y, x1 - center_x) * 180.0 / M_PI;
    
    // 计算角度跨度，考虑半径正负
    double angle_diff = end_angle - start_angle;
    if (radius < 0) {
        // 负半径：逆时针
        if (angle_diff <= 0) {
            angle_diff += 360;
        }
        params.startAngle = start_angle;
        params.spanAngle = angle_diff;
    } else {
        // 正半径：顺时针
        if (angle_diff >= 0) {
            angle_diff -= 360;
        }
        params.startAngle = start_angle;
        params.spanAngle = angle_diff;
    }
    
    params.center = QPointF(center_x, center_y);
    params.radius = abs_radius;
    params.isValid = true;
    
    return params;
}

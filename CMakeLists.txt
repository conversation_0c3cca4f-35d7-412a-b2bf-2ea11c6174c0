cmake_minimum_required(VERSION 3.16)
project(GeometryRenderer VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt6
find_package(Qt6 REQUIRED COMPONENTS Core Widgets)

# Enable automatic MOC, UIC, and RCC processing
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Source files
set(SOURCES
    main.cpp
    MainWindow.cpp
    GeometryRenderer.cpp
)

# Header files
set(HEADERS
    MainWindow.h
    GeometryRenderer.h
    CurveData.h
)

# Create executable
add_executable(GeometryRenderer ${SOURCES} ${HEADERS})

# Link Qt libraries
target_link_libraries(GeometryRenderer Qt6::Core Qt6::Widgets)

# Set output directory
set_target_properties(Geo<PERSON><PERSON><PERSON>er PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Platform-specific settings
if(WIN32)
    set_target_properties(GeometryRenderer PROPERTIES
        WIN32_EXECUTABLE TRUE
    )
endif()

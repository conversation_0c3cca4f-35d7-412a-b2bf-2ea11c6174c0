# 几何图形渲染器 (C++/Qt版本)

这是一个从Python/matplotlib项目转换而来的C++/Qt应用程序，用于渲染由直线段和圆弧段组成的几何图形。

## 功能特性

- 🎨 渲染复杂的几何图形（直线段 + 圆弧段）
- 🔍 支持鼠标缩放和平移操作
- 🎛️ 可自定义线条宽度和颜色
- 📐 可选的网格显示
- 📊 支持多组预定义曲线数据
- 🖥️ 现代化的Qt用户界面

## 系统要求

- **操作系统**: Windows 10/11, Linux, macOS
- **编译器**: 支持C++17的编译器 (GCC 7+, Clang 5+, MSVC 2017+)
- **Qt版本**: Qt 6.0 或更高版本
- **CMake**: 3.16 或更高版本

## 构建说明

### Windows (使用MinGW)

1. 确保已安装Qt6和CMake
2. 运行构建脚本:
   ```batch
   build.bat
   ```

### 手动构建

```bash
# 创建构建目录
mkdir build
cd build

# 配置CMake
cmake .. -DCMAKE_PREFIX_PATH=/path/to/qt6

# 编译
cmake --build . --config Release

# 运行
./bin/GeometryRenderer
```

## 使用说明

### 界面控制

- **曲线数据选择**: 在下拉菜单中选择不同的曲线组
- **线宽调整**: 使用数字输入框调整线条宽度 (1-10)
- **颜色选择**: 点击颜色按钮选择线条颜色
- **网格显示**: 勾选复选框显示/隐藏网格
- **视图控制**: 使用"适应视图"和"重置视图"按钮

### 鼠标操作

- **缩放**: 鼠标滚轮上下滚动
- **平移**: 按住左键拖拽
- **重置**: 使用工具栏按钮或菜单

### 快捷键

- `Ctrl+F`: 适应视图
- `Ctrl+R`: 重置视图
- `Ctrl+Q`: 退出程序

## 项目结构

```
GeometryRenderer/
├── CMakeLists.txt          # CMake构建配置
├── main.cpp                # 程序入口点
├── MainWindow.h/cpp        # 主窗口类
├── GeometryRenderer.h/cpp  # 几何图形渲染类
├── CurveData.h            # 曲线数据定义
├── build.bat              # Windows构建脚本
└── README.md              # 项目说明
```

## 技术实现

### 核心算法

1. **圆弧参数计算**: 根据起点、终点和半径计算圆心和角度范围
2. **坐标变换**: 世界坐标与屏幕坐标的双向转换
3. **视图管理**: 自动适应和手动控制的视图变换

### 关键特性

- **负半径支持**: 负半径表示逆时针圆弧，正半径表示顺时针圆弧
- **精确渲染**: 使用QPainter的高质量反锯齿渲染
- **交互式视图**: 支持平滑的缩放和平移操作
- **数据驱动**: 易于添加新的曲线数据组

## 从Python版本的主要改进

1. **性能提升**: C++/Qt比Python/matplotlib有更好的渲染性能
2. **交互性**: 实时的鼠标交互，无需重新绘制整个图形
3. **用户界面**: 专业的GUI界面，而非简单的图形窗口
4. **可扩展性**: 模块化设计，易于添加新功能

## 故障排除

### 常见问题

1. **Qt找不到**: 确保Qt6已正确安装并设置了CMAKE_PREFIX_PATH
2. **编译错误**: 检查C++17编译器支持
3. **运行时错误**: 确保Qt6运行时库在系统PATH中

### 调试信息

程序启动时会在控制台输出调试信息，包括:
- Qt版本信息
- 可用的UI样式
- 曲线段的详细计算过程

## 许可证

本项目基于MIT许可证开源。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

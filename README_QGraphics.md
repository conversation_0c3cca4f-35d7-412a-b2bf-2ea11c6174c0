# Python到Qt QGraphics转换

这个项目演示了如何将使用matplotlib的Python绘图代码转换为Qt的QGraphics框架实现。

## 文件结构

### 原始Python代码
- `script.py` - 原始的matplotlib绘图脚本

### Qt QGraphics版本
- `qgraphics_mainwindow.h/cpp` - 主窗口类，管理QGraphicsView和QGraphicsScene
- `curve_graphics_item.h/cpp` - 自定义QGraphicsItem，用于绘制曲线段
- `qgraphics_main.cpp` - 主函数
- `CMakeLists_qgraphics.txt` - CMake构建配置
- `build_and_run.py` - 构建和运行脚本

## 主要转换要点

### 1. 数据结构转换
**Python (matplotlib):**
```python
curves = [
    ((9794352.00, -0.67), 0.00),
    ((0.00, 0.00), -10454268.67),
    # ...
]
```

**Qt QGraphics:**
```cpp
struct CurveData {
    QPointF point;
    double radius;
};

QVector<CurveData> m_curves = {
    {QPointF(9794352.00, -0.67), 0.00},
    {QPointF(0.00, 0.00), -10454268.67},
    // ...
};
```

### 2. 绘图架构转换
**Python (matplotlib):**
- 使用`matplotlib.pyplot`和`matplotlib.patches.Arc`
- 直接在图形上绘制

**Qt QGraphics:**
- 使用`QGraphicsView`/`QGraphicsScene`架构
- 创建自定义`QGraphicsItem`来绘制每个曲线段
- 支持交互、缩放、平移

### 3. 圆弧计算
两个版本都使用相同的数学算法：
- 计算弦长和弦中点
- 根据半径正负确定圆心位置
- 计算起始角度和跨度角度
- 处理负半径（逆时针）和正半径（顺时针）

### 4. 坐标变换
**Python:**
```python
# matplotlib自动处理坐标系
ax.plot([x0, x1], [y0, y1], 'k-', lw=2)
```

**Qt:**
```cpp
// 手动应用缩放和Y轴翻转
QPointF scaledP0(p0.x() * m_scale + m_offsetX, m_offsetY - p0.y() * m_scale);
```

## 构建和运行

### 前提条件
- Qt 5.15+ (或Qt 6.x)
- CMake 3.14+
- C++17兼容编译器

### 构建步骤

1. **使用Python脚本构建:**
```bash
python build_and_run.py
```

2. **手动构建:**
```bash
mkdir build_qgraphics
cd build_qgraphics
cmake .. -f ../CMakeLists_qgraphics.txt
cmake --build . --config Release
```

3. **运行:**
```bash
./build_qgraphics/Release/QtGraphicsCurveDemo.exe
```

## 功能特性

### Qt QGraphics版本的优势
1. **交互性:** 支持鼠标拖拽、缩放
2. **性能:** 硬件加速渲染
3. **可扩展性:** 易于添加新的图形元素
4. **集成性:** 可以轻松集成到Qt应用程序中

### 控制功能
- **鼠标拖拽:** 平移视图
- **鼠标滚轮:** 缩放
- **重置视图按钮:** 恢复到适合窗口的视图
- **放大/缩小按钮:** 手动缩放控制

## 代码说明

### CurveGraphicsItem类
这是核心的绘图类，继承自`QGraphicsItem`：
- `boundingRect()`: 返回图形项的边界矩形
- `paint()`: 执行实际的绘制操作
- `calculateArcParams()`: 计算圆弧参数（与Python版本算法相同）

### QGraphicsMainWindow类
主窗口类，管理整个应用程序：
- 设置UI布局和控件
- 管理QGraphicsView和QGraphicsScene
- 处理用户交互（缩放、重置等）
- 加载和显示曲线数据

## 扩展建议

1. **添加更多曲线类型:** 可以创建其他QGraphicsItem子类
2. **数据导入:** 添加从文件读取曲线数据的功能
3. **导出功能:** 支持导出为图片或PDF
4. **动画:** 利用Qt的动画框架添加动态效果
5. **编辑功能:** 允许用户交互式编辑曲线

## 注意事项

1. **坐标系差异:** Qt使用左上角为原点，Y轴向下，需要进行坐标变换
2. **角度单位:** Qt的drawArc使用1/16度为单位
3. **内存管理:** QGraphicsItem由QGraphicsScene自动管理内存
4. **性能优化:** 对于大量图形项，考虑使用LOD（细节层次）技术

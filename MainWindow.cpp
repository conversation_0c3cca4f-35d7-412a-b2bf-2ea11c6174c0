#include "MainWindow.h"
#include <QApplication>
#include <QMessageBox>
#include <QColorDialog>
#include <QSplitter>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_renderer(nullptr)
    , m_currentLineColor(Qt::black)
{
    setupUI();
    setupMenuBar();
    setupToolBar();
    setupStatusBar();
    createConnections();
    
    // 设置窗口属性
    setWindowTitle("几何图形渲染器 - C++/Qt版本");
    setMinimumSize(1000, 700);
    resize(1200, 800);
    
    // 默认加载第二组曲线数据
    m_curveSelector->setCurrentIndex(1);
    onCurveDataChanged();
}

void MainWindow::setupUI() {
    // 创建中央部件
    QWidget* centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);
    
    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(centralWidget);
    
    // 创建控制面板
    QHBoxLayout* controlLayout = new QHBoxLayout();
    
    // 曲线数据选择器
    controlLayout->addWidget(new QLabel("曲线数据:"));
    m_curveSelector = new QComboBox();
    m_curveSelector->addItem("曲线组1", 0);
    m_curveSelector->addItem("曲线组2", 1);
    controlLayout->addWidget(m_curveSelector);
    
    controlLayout->addSpacing(20);
    
    // 线宽控制
    controlLayout->addWidget(new QLabel("线宽:"));
    m_lineWidthSpinBox = new QSpinBox();
    m_lineWidthSpinBox->setRange(1, 10);
    m_lineWidthSpinBox->setValue(2);
    controlLayout->addWidget(m_lineWidthSpinBox);
    
    controlLayout->addSpacing(20);
    
    // 颜色选择
    controlLayout->addWidget(new QLabel("线条颜色:"));
    m_colorButton = new QPushButton();
    m_colorButton->setFixedSize(30, 25);
    m_colorButton->setStyleSheet("background-color: black; border: 1px solid gray;");
    controlLayout->addWidget(m_colorButton);
    
    controlLayout->addSpacing(20);
    
    // 网格显示
    m_showGridCheckBox = new QCheckBox("显示网格");
    m_showGridCheckBox->setChecked(true);
    controlLayout->addWidget(m_showGridCheckBox);
    
    controlLayout->addSpacing(20);
    
    // 视图控制按钮
    m_fitViewButton = new QPushButton("适应视图");
    m_resetViewButton = new QPushButton("重置视图");
    controlLayout->addWidget(m_fitViewButton);
    controlLayout->addWidget(m_resetViewButton);
    
    controlLayout->addStretch();
    
    // 添加控制面板到主布局
    mainLayout->addLayout(controlLayout);
    
    // 创建渲染器
    m_renderer = new GeometryRenderer(this);
    mainLayout->addWidget(m_renderer, 1);
}

void MainWindow::setupMenuBar() {
    // 文件菜单
    QMenu* fileMenu = menuBar()->addMenu("文件(&F)");
    
    m_exitAction = new QAction("退出(&X)", this);
    m_exitAction->setShortcut(QKeySequence::Quit);
    fileMenu->addAction(m_exitAction);
    
    // 视图菜单
    QMenu* viewMenu = menuBar()->addMenu("视图(&V)");
    
    m_fitViewAction = new QAction("适应视图(&F)", this);
    m_fitViewAction->setShortcut(QKeySequence("Ctrl+F"));
    viewMenu->addAction(m_fitViewAction);
    
    m_resetViewAction = new QAction("重置视图(&R)", this);
    m_resetViewAction->setShortcut(QKeySequence("Ctrl+R"));
    viewMenu->addAction(m_resetViewAction);
    
    // 帮助菜单
    QMenu* helpMenu = menuBar()->addMenu("帮助(&H)");
    
    m_aboutAction = new QAction("关于(&A)", this);
    helpMenu->addAction(m_aboutAction);
}

void MainWindow::setupToolBar() {
    QToolBar* toolBar = addToolBar("主工具栏");
    toolBar->addAction(m_fitViewAction);
    toolBar->addAction(m_resetViewAction);
}

void MainWindow::setupStatusBar() {
    m_statusLabel = new QLabel("就绪");
    m_coordLabel = new QLabel("坐标: (0, 0)");
    
    statusBar()->addWidget(m_statusLabel);
    statusBar()->addPermanentWidget(m_coordLabel);
}

void MainWindow::createConnections() {
    // 控件连接
    connect(m_curveSelector, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &MainWindow::onCurveDataChanged);
    connect(m_lineWidthSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &MainWindow::onLineWidthChanged);
    connect(m_showGridCheckBox, &QCheckBox::toggled,
            this, &MainWindow::onShowGridToggled);
    connect(m_colorButton, &QPushButton::clicked,
            this, &MainWindow::onChooseLineColor);
    connect(m_fitViewButton, &QPushButton::clicked,
            this, &MainWindow::onFitToView);
    connect(m_resetViewButton, &QPushButton::clicked,
            this, &MainWindow::onResetView);
    
    // 菜单动作连接
    connect(m_exitAction, &QAction::triggered, this, &QWidget::close);
    connect(m_fitViewAction, &QAction::triggered, this, &MainWindow::onFitToView);
    connect(m_resetViewAction, &QAction::triggered, this, &MainWindow::onResetView);
    connect(m_aboutAction, &QAction::triggered, this, &MainWindow::showAbout);
}

void MainWindow::onCurveDataChanged() {
    if (!m_renderer) return;
    
    int index = m_curveSelector->currentData().toInt();
    std::vector<CurveSegment> curves;
    
    if (index == 0) {
        curves = CurveData::getCurves1();
        m_statusLabel->setText("已加载曲线组1");
    } else {
        curves = CurveData::getCurves2();
        m_statusLabel->setText("已加载曲线组2");
    }
    
    m_renderer->setCurveData(curves);
}

void MainWindow::onLineWidthChanged(int width) {
    if (m_renderer) {
        m_renderer->setLineWidth(width);
    }
}

void MainWindow::onShowGridToggled(bool show) {
    if (m_renderer) {
        m_renderer->setShowGrid(show);
    }
}

void MainWindow::onChooseLineColor() {
    QColor color = QColorDialog::getColor(m_currentLineColor, this, "选择线条颜色");
    if (color.isValid()) {
        m_currentLineColor = color;
        m_colorButton->setStyleSheet(
            QString("background-color: %1; border: 1px solid gray;").arg(color.name()));
        if (m_renderer) {
            m_renderer->setLineColor(color);
        }
    }
}

void MainWindow::onFitToView() {
    // 重新计算并适应视图
    if (m_renderer) {
        onCurveDataChanged(); // 这会触发fitToView
        m_statusLabel->setText("视图已适应");
    }
}

void MainWindow::onResetView() {
    if (m_renderer) {
        onCurveDataChanged(); // 重新加载数据并重置视图
        m_statusLabel->setText("视图已重置");
    }
}

void MainWindow::showAbout() {
    QMessageBox::about(this, "关于",
        "几何图形渲染器 v1.0\n\n"
        "这是一个从Python/matplotlib转换而来的C++/Qt应用程序。\n"
        "功能包括:\n"
        "• 渲染由直线段和圆弧段组成的几何图形\n"
        "• 支持缩放和平移\n"
        "• 可自定义线条样式\n"
        "• 网格显示\n\n"
        "使用方法:\n"
        "• 鼠标滚轮: 缩放\n"
        "• 左键拖拽: 平移\n"
        "• 工具栏按钮: 视图控制");
}

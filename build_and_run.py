#!/usr/bin/env python3
"""
构建和运行Qt QGraphics版本的脚本
这个脚本演示了如何从Python代码转换到Qt QGraphics实现
"""

import subprocess
import sys
import os

def build_qt_project():
    """构建Qt项目"""
    print("正在构建Qt QGraphics项目...")
    
    # 创建构建目录
    build_dir = "build_qgraphics"
    if not os.path.exists(build_dir):
        os.makedirs(build_dir)
    
    # 进入构建目录
    os.chdir(build_dir)
    
    try:
        # 运行cmake配置
        print("运行CMake配置...")
        subprocess.run(["cmake", "..", "-f", "../CMakeLists_qgraphics.txt"], check=True)
        
        # 构建项目
        print("构建项目...")
        subprocess.run(["cmake", "--build", ".", "--config", "Release"], check=True)
        
        print("构建成功！")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"构建失败: {e}")
        return False
    except FileNotFoundError:
        print("错误: 找不到CMake。请确保CMake已安装并在PATH中。")
        return False
    finally:
        # 返回原目录
        os.chdir("..")

def run_qt_application():
    """运行Qt应用程序"""
    executable_path = os.path.join("build_qgraphics", "Release", "QtGraphicsCurveDemo.exe")
    
    if os.path.exists(executable_path):
        print("运行Qt QGraphics应用程序...")
        subprocess.run([executable_path])
    else:
        print(f"错误: 找不到可执行文件 {executable_path}")

def main():
    print("=" * 60)
    print("Python到Qt QGraphics转换演示")
    print("=" * 60)
    
    print("\n原始Python代码特点:")
    print("- 使用matplotlib绘制曲线")
    print("- 支持直线段和圆弧段")
    print("- 处理正负半径（顺时针/逆时针）")
    print("- 包含两组曲线数据")
    
    print("\nQt QGraphics版本特点:")
    print("- 使用QGraphicsView/QGraphicsScene架构")
    print("- 自定义QGraphicsItem绘制曲线段")
    print("- 支持缩放、平移、交互")
    print("- 保持与Python版本相同的数学计算")
    
    print("\n开始构建...")
    
    if build_qt_project():
        print("\n构建完成，启动应用程序...")
        run_qt_application()
    else:
        print("\n构建失败，请检查错误信息。")

if __name__ == "__main__":
    main()

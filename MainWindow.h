#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMenuBar>
#include <QStatusBar>
#include <QToolBar>
#include <QPushButton>
#include <QComboBox>
#include <QSpinBox>
#include <QCheckBox>
#include <QLabel>
#include <QAction>
#include <QColorDialog>
#include "GeometryRenderer.h"

class MainWindow : public QMainWindow {
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow() = default;

private slots:
    void onCurveDataChanged();
    void onLineWidthChanged(int width);
    void onShowGridToggled(bool show);
    void onChooseLineColor();
    void onFitToView();
    void onResetView();
    void showAbout();

private:
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupStatusBar();
    void createConnections();

private:
    // 主要组件
    GeometryRenderer* m_renderer;
    
    // UI控件
    QComboBox* m_curveSelector;
    QSpinBox* m_lineWidthSpinBox;
    QCheckBox* m_showGridCheckBox;
    QPushButton* m_colorButton;
    QPushButton* m_fitViewButton;
    QPushButton* m_resetViewButton;
    
    // 菜单和工具栏
    QAction* m_fitViewAction;
    QAction* m_resetViewAction;
    QAction* m_exitAction;
    QAction* m_aboutAction;
    
    // 状态栏
    QLabel* m_statusLabel;
    QLabel* m_coordLabel;
    
    // 当前设置
    QColor m_currentLineColor;
};

#endif // MAINWINDOW_H

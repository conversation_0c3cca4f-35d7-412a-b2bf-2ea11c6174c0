#pragma once

#include <QMainWindow>
#include <QVector>
#include <QPointF>
#include <QPainter>

struct CurveSegment {
    QPointF p0;
    QPointF p1;
    double radius;
};

class MainWindow : public QMainWindow
{
    Q_OBJECT
public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();
protected:
    void paintEvent(QPaintEvent *event) override;
private:
    void drawArcSegment(QPainter& painter, const CurveSegment& seg,
                       double scale, double offsetX, double offsetY);
    QVector<CurveSegment> curves;
};
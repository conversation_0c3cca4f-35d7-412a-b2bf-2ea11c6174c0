#ifndef CURVEDATA_H
#define CURVEDATA_H

#include <vector>
#include <utility>

// 点结构
struct Point {
    double x;
    double y;
    
    Point(double x = 0.0, double y = 0.0) : x(x), y(y) {}
};

// 曲线段结构 - 包含起点和半径
struct CurveSegment {
    Point point;
    double radius;
    
    CurveSegment(const Point& p, double r) : point(p), radius(r) {}
    CurveSegment(double x, double y, double r) : point(x, y), radius(r) {}
};

// 圆弧参数结构
struct ArcParams {
    Point center;
    double radius;
    double startAngle;  // 起始角度（度）
    double endAngle;    // 结束角度（度）
    bool isValid;
    
    ArcParams() : isValid(false) {}
    ArcParams(const Point& c, double r, double start, double end) 
        : center(c), radius(r), startAngle(start), endAngle(end), isValid(true) {}
};

// 预定义的曲线数据
class CurveData {
public:
    static std::vector<CurveSegment> getCurves1() {
        return {
            {{9794352.00, -0.67}, 0.00},
            {{0.00, 0.00}, -10454268.67},
            {{4778222.00, 8779192.67}, -10454268.67},
            {{181229.33, 17438526.00}, 0.00},
            {{180452.00, 17449800.00}, 0.00},
            {{9773872.67, 17449799.33}, 0.00},
            {{9794352.00, -0.67}, 0.00}
        };
    }
    
    static std::vector<CurveSegment> getCurves2() {
        return {
            {{26017738.67, 17348200.0}, 0.0},
            {{25992338.67, 304800.0}, 0.0},
            {{21513800.0, 304800.0}, 0.0},
            {{21513800.0, 3362410.0}, 6083300.0},
            {{17360900.0, 9131300.0}, 6083300.0},
            {{21564600.0, 14916941.33}, 0.0},
            {{21564600.0, 17348200.0}, 0.0},
            {{26017738.67, 17348200.0}, 0.0}
        };
    }
};

#endif // CURVEDATA_H

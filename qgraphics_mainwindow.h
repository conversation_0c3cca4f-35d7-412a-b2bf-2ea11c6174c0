#pragma once

#include <QMainWindow>
#include <QGraphicsView>
#include <QGraphicsScene>
#include <QGraphicsItem>
#include <QVector>
#include <QPointF>
#include <QVBoxLayout>
#include <QWidget>

struct CurveData {
    QPointF point;
    double radius;
};

class QGraphicsMainWindow : public QMainWindow
{
    Q_OBJECT

public:
    QGraphicsMainWindow(QWidget *parent = nullptr);
    ~QGraphicsMainWindow();

private slots:
    void resetView();
    void zoomIn();
    void zoomOut();

private:
    void setupUI();
    void setupData();
    void drawCurves();
    void addMenuBar();
    
    QGraphicsView *m_view;
    QGraphicsScene *m_scene;
    QVector<CurveData> m_curves;
    QVector<CurveData> m_curves2;
    
    // 缩放和偏移参数
    double m_scale;
    double m_offsetX;
    double m_offsetY;
};

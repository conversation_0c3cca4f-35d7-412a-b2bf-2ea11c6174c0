#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QStandardPaths>
#include <QDebug>
#include "MainWindow.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 设置应用程序信息
    app.setApplicationName("GeometryRenderer");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("GeometryRenderer");
    app.setApplicationDisplayName("几何图形渲染器");
    
    // 设置应用程序样式
    app.setStyle(QStyleFactory::create("Fusion"));
    
    // 设置调色板（可选，提供更现代的外观）
    QPalette darkPalette;
    darkPalette.setColor(QPalette::Window, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::WindowText, Qt::white);
    darkPalette.setColor(QPalette::Base, QColor(25, 25, 25));
    darkPalette.setColor(QPalette::AlternateBase, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ToolTipBase, Qt::white);
    darkPalette.setColor(QPalette::ToolTipText, Qt::white);
    darkPalette.setColor(QPalette::Text, Qt::white);
    darkPalette.setColor(QPalette::Button, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ButtonText, Qt::white);
    darkPalette.setColor(QPalette::BrightText, Qt::red);
    darkPalette.setColor(QPalette::Link, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::Highlight, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::HighlightedText, Qt::black);
    
    // 注释掉暗色主题，使用默认主题以便更好地显示图形
    // app.setPalette(darkPalette);
    
    try {
        // 创建并显示主窗口
        MainWindow window;
        window.show();
        
        qDebug() << "应用程序启动成功";
        qDebug() << "Qt版本:" << QT_VERSION_STR;
        qDebug() << "可用样式:" << QStyleFactory::keys();
        
        return app.exec();
    }
    catch (const std::exception& e) {
        qCritical() << "应用程序异常:" << e.what();
        return -1;
    }
    catch (...) {
        qCritical() << "未知异常";
        return -1;
    }
}

import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Arc

# 提取的点和半径数据（从图像读取）
curves = [
    ((9794352.00, -0.67), 0.00),
    ((0.00, 0.00), -10454268.67),
    ((4778222.00, 8779192.67), -10454268.67),
    ((181229.33, 17438526.00), 0.00),
    ((180452.00, 17449800.00), 0.00),
    ((9773872.67, 17449799.33), 0.00),
    ((9794352.00, -0.67), 0.00)
]

curves2 = [
    ((26017738.67, 17348200.0), 0.0),
    ((25992338.67, 304800.0), 0.0),
    ((21513800.0, 304800.0), 0.0),
    ((21513800.0, 3362410.0), 6083300.0),
    ((17360900.0, 9131300.0), 6083300.0),
    ((21564600.0, 14916941.33), 0.0),
    ((21564600.0, 17348200.0), 0.0),
    ((26017738.67, 17348200.0), 0.0)
]
curves3 = [
    ((8432800.0, 14020700.0), -100.0),
    ((8432900.0, 14020800.0), 0.0),
    ((8432900.0, 15163800.0), -100.0),
    ((8432800.0, 15163900.0), 0.0),
    ((6350000.0, 15163900.0), -100.0),
    ((6349900.0, 15163800.0), 0.0),
    ((6349900.0, 13970000.0), -100.0),
    ((6350000.0, 13969900.0), 0.0),
    ((6934200.0, 13969900.0), -100.0),
    ((6934300.0, 13970000.0), 0.0),
    ((6934300.0, 14731900.0), -100.0),
    ((6934600.0, 14731900.0), 0.0),
    ((7746900.0, 14020800.0), -100.0),
    ((7747000.0, 14020700.0), 0.0),
    ((8432800.0, 14020700.0), 0.0),

    ((9794352.0, -0.667), 0.0),
    ((177800.0, -0.667), -10489522.666999999),
    ((5870337.3329999996, 8313088.667000004), -10489522.666999999),
    ((533400.0, 17449800.0), 0.0),
    ((9773872.666999994, 17449799.333000001), 0.0),
    ((9794352.0, -0.667), 0.0)
]

def get_arc_params(x0, y0, x1, y1, radius):
    # 计算弦长
    chord_length = np.sqrt((x1 - x0)**2 + (y1 - y0)**2)
    if abs(radius) < chord_length / 2:
        return None  # 半径太小，退化为直线

    # 计算弦中点
    mid_x = (x0 + x1) / 2
    mid_y = (y0 + y1) / 2

    # 计算从弦中点到圆心的距离
    abs_radius = abs(radius)
    h = np.sqrt(abs_radius**2 - (chord_length / 2)**2)

    # 计算垂直于弦的单位向量
    chord_dx = x1 - x0
    chord_dy = y1 - y0

    perp_dx = chord_dy / chord_length
    perp_dy = chord_dx / chord_length

    # 根据半径正负确定圆心
    if radius < 0:
        # 负半径：逆时针方向
        center_x = mid_x - h * perp_dx
        center_y = mid_y + h * perp_dy
    else:
        # 正半径：顺时针方向
        center_x = mid_x + h * perp_dx
        center_y = mid_y - h * perp_dy

    # 计算起止角（以度为单位，matplotlib Arc 需要）
    start_angle = np.degrees(np.arctan2(y0 - center_y, x0 - center_x))
    end_angle = np.degrees(np.arctan2(y1 - center_y, x1 - center_x))

    # 正确处理角度差，考虑半径正负
    if radius < 0:
        # 负半径：逆时针，matplotlib的Arc需要theta2 > theta1
        # 但我们需要确保是逆时针方向
        angle_diff = end_angle - start_angle
        if angle_diff <= 0:
            angle_diff += 360
        theta1 = start_angle
        theta2 = start_angle + angle_diff
    else:
        # 正半径：顺时针，需要调整角度
        angle_diff = end_angle - start_angle
        if angle_diff <= 0:
            angle_diff += 360
        theta1 = start_angle+ angle_diff
        theta2 = start_angle

    return center_x, center_y, abs_radius, theta1, theta2

fig, ax = plt.subplots(figsize=(10, 12))

for i in range(len(curves3) - 1):
    (x0, y0), r = curves3[i]
    (x1, y1), _ = curves3[i + 1]
    print(f"段{i}: 起点=({x0}, {y0}), 终点=({x1}, {y1}), 半径={r}")  # 调试输出
    if r == 0:
        # 直线段
        ax.plot([x0, x1], [y0, y1], 'k-', lw=2)
    else:
        # 圆弧段
        arc_params = get_arc_params(x0, y0, x1, y1, r)
        if arc_params is not None:
            center_x, center_y, abs_radius, theta1, theta2 = arc_params
            print(f"  圆心=({center_x}, {center_y}), 半径={abs_radius}, theta1={theta1}, theta2={theta2}")  # 调试输出
            # 自动判断theta1和theta2的方向，确保弧段连接正确
            arc = Arc((center_x, center_y), 2*abs_radius, 2*abs_radius,
                     angle=0, theta1=theta1, theta2=theta2, color='k', lw=2)
            ax.add_patch(arc)
        else:
            # 半径太小，退化为直线
            ax.plot([x0, x1], [y0, y1], 'k-', lw=2)

# 闭合图形（如果最后一个点和第一个点不同）
(x0, y0), _ = curves3[-1]
(x1, y1), _ = curves3[0]
if (x0, y0) != (x1, y1):
    ax.plot([x0, x1], [y0, y1], 'k-', lw=2)

ax.set_aspect('equal')
ax.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()
